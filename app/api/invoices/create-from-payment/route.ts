// app/api/invoices/create-from-payment/route.ts
import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { generateInvoiceNumber } from '@/lib/invoices';
import type { Database } from '@/types/supabase';

export async function POST(request: Request) {
  try {
    const { paymentId } = await request.json();

    if (!paymentId) {
      return NextResponse.json(
        { error: 'Payment ID is required' },
        { status: 400 }
      );
    }

    const supabase = createClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    // Get payment details with related data
    const { data: payment, error: paymentError } = await supabase
      .from('pliromes')
      .select(`
        *,
        pelates (id, name, last_name, afm),
        programs (id, name)
      `)
      .eq('id', paymentId)
      .single();

    if (paymentError || !payment) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      );
    }

    // Check if payment already has an associated invoice
    const { data: existingInvoice } = await supabase
      .from('invoices')
      .select('id')
      .eq('client_id', payment.pelates_id)
      .eq('total_gross', payment.money_gave)
      .eq('issue_date', payment.date_money_gave?.split('T')[0])
      .single();

    if (existingInvoice) {
      return NextResponse.json(
        { error: 'An invoice for this payment may already exist' },
        { status: 400 }
      );
    }

    // Get next invoice number
    const invoiceNumber = await generateInvoiceNumber(supabase, 'A');

    // Calculate VAT (assuming 24% VAT rate)
    const vatRate = 0.24;
    const grossAmount = payment.money_gave || 0;
    const netAmount = grossAmount / (1 + vatRate);
    const vatAmount = grossAmount - netAmount;

    // Create invoice
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert({
        invoice_series: 'A',
        invoice_number: invoiceNumber,
        issue_date: payment.date_money_gave?.split('T')[0] || new Date().toISOString().split('T')[0],
        invoice_type: '11.2', // Receipt
        client_id: payment.pelates_id,
        client_name: payment.pelates ? `${payment.pelates.name} ${payment.pelates.last_name}` : 'Unknown Client',
        client_vat: payment.pelates?.afm || '',
        client_country: 'GR',
        total_net: netAmount,
        total_vat: vatAmount,
        total_gross: grossAmount,
        currency: 'EUR',
        status: 'draft'
      })
      .select()
      .single();

    if (invoiceError) {
      console.error('Error creating invoice:', invoiceError);
      return NextResponse.json(
        { error: 'Failed to create invoice' },
        { status: 500 }
      );
    }

    // Create invoice line
    const { error: lineError } = await supabase
      .from('invoice_lines')
      .insert({
        invoice_id: invoice.id,
        line_number: 1,
        description: payment.programs?.name
          ? `Συνδρομή ${payment.programs.name}`
          : 'Συνδρομή γυμναστηρίου',
        quantity: 1,
        unit_price: netAmount,
        net_value: netAmount,
        vat_category: 1, // 24%
        vat_amount: vatAmount,
        income_classification_type: 'E3_561_007',
        income_classification_category: 'category1_3'
      });

    if (lineError) {
      console.error('Error creating invoice line:', lineError);
      // Continue even if line creation fails
    }

    // Create payment method based on the payment's way_of_payment
    let paymentType = 4; // Default to cash
    switch (payment.way_of_payment) {
      case 'CASH':
        paymentType = 4;
        break;
      case 'VIVA':
      case 'WINBANK':
        paymentType = 3; // Bank payment
        break;
      default:
        paymentType = 4;
    }

    const { error: paymentMethodError } = await supabase
      .from('invoice_payment_methods')
      .insert({
        invoice_id: invoice.id,
        payment_type: paymentType,
        amount: grossAmount,
        payment_info: payment.comments || `Payment via ${payment.way_of_payment}`
      });

    if (paymentMethodError) {
      console.error('Error creating payment method:', paymentMethodError);
      // Continue even if payment method creation fails
    }

    return NextResponse.json({
      success: true,
      invoice,
      message: 'Invoice created successfully'
    });

  } catch (error) {
    console.error('Error in create-from-payment API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
