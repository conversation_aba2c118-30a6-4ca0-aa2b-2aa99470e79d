// app/api/mydata/set-environment/route.ts
import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  try {
    const { environment } = await request.json();

    if (!environment || !['development', 'production'].includes(environment)) {
      return NextResponse.json(
        { error: 'Invalid environment. Must be "development" or "production"' },
        { status: 400 }
      );
    }

    // Set a cookie to override the environment for this session
    // This is a temporary solution - in production you might want to store this in a database
    const cookieStore = cookies();
    cookieStore.set('mydata-environment', environment, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    });

    return NextResponse.json({
      success: true,
      environment,
      message: `Environment set to ${environment}`
    });

  } catch (error) {
    console.error('Error setting environment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
