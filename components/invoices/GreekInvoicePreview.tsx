// components/invoices/FullPageGreekInvoice.tsx
'use client';

import React, { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Printer, Download, ArrowLeft, X } from 'lucide-react';

// Types
interface InvoiceData {
  id: string;
  invoice_series: string;
  invoice_number: string;
  issue_date: string;
  client_name: string;
  client_vat: string;
  client_country: string;
  invoice_type: string;
  currency: string;
  total_net: number;
  total_vat: number;
  total_gross: number;
  mark?: string | null;
  qr_url?: string | null;
  created_at: string;
  updated_at: string;
  status: string;
}

interface InvoiceLine {
  id: string;
  invoice_id: string;
  line_number: number;
  description: string;
  quantity: number;
  unit_price: number;
  net_value: number;
  vat_category: number;
  vat_amount: number;
  income_classification_type: string;
  income_classification_category: string;
}

interface PaymentMethod {
  id: string;
  invoice_id: string;
  payment_type: number;
  amount: number;
  payment_info?: string | null;
}

interface CompanySettings {
  id: string;
  companyName: string;
  vatNumber: string;
  country: string;
  branch: string;
  address?: string;
  postalCode?: string;
  city?: string;
  profession?: string;
  taxOffice?: string;
  defaultClassificationType: string;
  defaultClassificationCategory: string;
}

interface FullPageGreekInvoiceProps {
  invoice: InvoiceData;
  lines: InvoiceLine[];
  paymentMethods: PaymentMethod[];
  companySettings: CompanySettings;
}

// VAT rate mapping
const VAT_RATES: Record<number, string> = {
  1: '24%',
  2: '13%',
  3: '6%',
  4: '17%',
  5: '9%',
  6: '4%',
  7: '0%',
  8: '0%'
};

// Payment method mapping
const PAYMENT_METHODS: Record<number, string> = {
  1: 'Μετρητά',
  2: 'Επιταγή',
  3: 'Τραπεζικό έμβασμα',
  4: 'Πιστωτική κάρτα',
  7: 'POS / e-POS'
};

// Format currency for Greek locale
const formatCurrency = (amount: number): string => {
  return amount.toFixed(2).replace('.', ',');
};

// Format date for Greek locale
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('el-GR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch (error) {
    return dateString;
  }
};

export default function FullPageGreekInvoice({
  invoice,
  lines,
  paymentMethods,
  companySettings
}: FullPageGreekInvoiceProps) {

  // Get primary payment method
  const primaryPayment = paymentMethods?.[0];
  const paymentMethodText = primaryPayment
    ? PAYMENT_METHODS[primaryPayment.payment_type] || `Τύπος ${primaryPayment.payment_type}`
    : 'POS / e-POS';

  // Set up full page layout and hide any parent navigation
  useEffect(() => {
    // Override any parent styles
    document.documentElement.style.height = '100vh';
    document.documentElement.style.overflow = 'hidden';
    document.body.style.margin = '0';
    document.body.style.padding = '0';
    document.body.style.backgroundColor = '#f5f5f5';
    document.body.style.height = '100vh';
    document.body.style.overflow = 'auto';

    // Hide any parent elements that might contain navigation
    const rootElement = document.getElementById('root') || document.getElementById('__next');
    if (rootElement) {
      rootElement.style.height = '100vh';
      rootElement.style.overflow = 'hidden';
    }

    return () => {
      document.documentElement.style.height = '';
      document.documentElement.style.overflow = '';
      document.body.style.margin = '';
      document.body.style.padding = '';
      document.body.style.backgroundColor = '';
      document.body.style.height = '';
      document.body.style.overflow = '';
    };
  }, []);

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadPDF = () => {
    // Use browser's print to PDF functionality
    window.print();
  };

  const handleClose = () => {
    window.close();
  };

  const handleGoBack = () => {
    window.history.back();
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: '#f5f5f5',
      zIndex: 9999,
      overflow: 'auto'
    }}>
      {/* Floating Action Buttons - Hidden on Print */}
      <div className="floating-actions no-print">
        <Button onClick={handleGoBack} size="sm" variant="outline" className="action-btn">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <Button onClick={handlePrint} size="sm" variant="outline" className="action-btn">
          <Printer className="h-4 w-4" />
        </Button>
        <Button onClick={handleDownloadPDF} size="sm" className="action-btn">
          <Download className="h-4 w-4" />
        </Button>
        <Button onClick={handleClose} size="sm" variant="outline" className="action-btn">
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Invoice Page */}
      <div className="invoice-page">
        {/* Company Information Header */}
        <div className="company-header">
          <div className="info-row">
            <div className="info-label">Επωνυμία</div>
            <div className="info-value">{companySettings.companyName}</div>
          </div>
          <div className="info-row">
            <div className="info-label">Α.Φ.Μ.</div>
            <div className="info-value">{companySettings.vatNumber}</div>
          </div>
          <div className="info-row">
            <div className="info-label">Επάγγελμα</div>
            <div className="info-value">
              {companySettings.profession || 'Υπηρεσίες Γυμναστηρίου'}
            </div>
          </div>
          <div className="info-row">
            <div className="info-label">Δ.Ο.Υ.</div>
            <div className="info-value">
              {companySettings.taxOffice || 'ΚΑΛΑΜΑΡΙΑΣ'}
            </div>
          </div>
          <div className="info-row">
            <div className="info-label">Διεύθυνση</div>
            <div className="info-value">
              {companySettings.address && companySettings.city
                ? `${companySettings.address} - ${companySettings.city}, Τ.Κ: ${companySettings.postalCode || ''}`
                : 'ΛΕΟΦΩΡΟΣ ΘΕΣΣΑΛΟΝΙΚΗΣ ΝΗΧΑΝΙΩΝΑΣ 30 - ΠΕΡΑΙΑ, Τ.Κ: 57019'
              }
            </div>
          </div>
        </div>

        {/* Invoice Type Header */}
        <div className="invoice-type-header">
          ΑΠΥ (Απόδειξη Παροχής Υπηρεσιών)
        </div>

        {/* Invoice Details Row */}
        <div className="invoice-details">
          <div className="detail-group">
            <span className="detail-label">Σειρά</span>
            <div className="detail-value">{invoice.invoice_series || 'A'}</div>
          </div>
          <div className="detail-group">
            <span className="detail-label">Α.Α.</span>
            <div className="detail-value">{invoice.invoice_number}</div>
          </div>
          <div className="detail-group">
            <span className="detail-label">Ημερομηνία</span>
            <div className="detail-value">{formatDate(invoice.issue_date)}</div>
          </div>
          <div className="detail-group">
            <span className="detail-label">ΜΑΡΚ</span>
            <div className="detail-value">
              {invoice.mark ? invoice.mark.substring(0, 15) : '400001951013818'}
            </div>
          </div>
          <div className="detail-group">
            <span className="detail-label">Τρόπος Πληρωμής</span>
            <div className="detail-value">{paymentMethodText}</div>
          </div>
        </div>

        {/* Client Details */}
        <div className="client-section">
          <div className="client-header">Στοιχεία Πελάτη</div>
          <div className="client-info">
            <div className="client-row">
              <span className="client-label">Α.Φ.Μ.:</span>
              <div className="client-value"></div>
            </div>
            <div className="client-row">
              <span className="client-label">Επωνυμία:</span>
              <div className="client-value">{invoice.client_name}</div>
            </div>
            <div className="client-row">
              <span className="client-label">Διεύθυνση:</span>
              <div className="client-value">
                - {companySettings.city || 'ΠΕΡΑΙΑ'} {companySettings.postalCode || '57010'}
              </div>
            </div>
          </div>
        </div>

        {/* Invoice Lines Table */}
        <div className="invoice-table">
          <div className="table-header">
            <div className="col-code">Κωδ.</div>
            <div className="col-description">Περιγραφή</div>
            <div className="col-quantity">Ποσότητα</div>
            <div className="col-unit">Μ.Μ.</div>
            <div className="col-price">Τιμή (€)</div>
            <div className="col-discount">Έκπτωση (€)</div>
            <div className="col-value">Αξία (€)</div>
            <div className="col-vat-percent">ΦΠΑ %</div>
            <div className="col-vat-amount">ΦΠΑ (€)</div>
            <div className="col-total">Τελ. Αξία (€)</div>
          </div>

          {lines.map((line) => (
            <div key={line.id} className="table-row">
              <div className="col-code">{line.line_number}</div>
              <div className="col-description">{line.description}</div>
              <div className="col-quantity">{formatCurrency(line.quantity)}</div>
              <div className="col-unit"></div>
              <div className="col-price">{formatCurrency(line.unit_price)}</div>
              <div className="col-discount">0,00</div>
              <div className="col-value">{formatCurrency(line.net_value)}</div>
              <div className="col-vat-percent">{VAT_RATES[line.vat_category] || `${line.vat_category}%`}</div>
              <div className="col-vat-amount">{formatCurrency(line.vat_amount)}</div>
              <div className="col-total">{formatCurrency(line.net_value + line.vat_amount)}</div>
            </div>
          ))}

          {/* Totals Row */}
          <div className="table-row totals-row">
            <div className="col-code"></div>
            <div className="col-description"><strong>Σύνολα</strong></div>
            <div className="col-quantity"></div>
            <div className="col-unit"></div>
            <div className="col-price"></div>
            <div className="col-discount">0,00</div>
            <div className="col-value"><strong>{formatCurrency(invoice.total_net)}</strong></div>
            <div className="col-vat-percent"></div>
            <div className="col-vat-amount"><strong>{formatCurrency(invoice.total_vat)}</strong></div>
            <div className="col-total"><strong>{formatCurrency(invoice.total_gross)}</strong></div>
          </div>
        </div>

        {/* Summary Section */}
        <div className="summary-section">
          <div className="summary-header">
            <div className="summary-col">Συνολ. Αξία</div>
            <div className="summary-col">(-) Παρακρατούμενοι</div>
            <div className="summary-col">Παρακρατούμενοι (πλφ)</div>
            <div className="summary-col">(-) Κρατήσεις</div>
            <div className="summary-col">Κρατήσεις (πλφ)</div>
            <div className="summary-col">(+) Χαρτόσημο</div>
            <div className="summary-col">(+) Τέλη</div>
            <div className="summary-col">(+) Λοιποί Φόροι</div>
          </div>
          <div className="summary-row">
            <div className="summary-col">{formatCurrency(invoice.total_gross)}</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
            <div className="summary-col">0,00</div>
          </div>
        </div>

        {/* Total Payable */}
        <div className="total-payable">
          <strong>Πληρωτέο (€): {formatCurrency(invoice.total_gross)}</strong>
        </div>

        {/* Notes Section */}
        <div className="notes-section">
          <div className="notes-label">Παρατηρήσεις:</div>
        </div>

        {/* Footer with QR Code */}
        <div className="footer">
          <div className="footer-left">
            {invoice.qr_url && (
              <div className="qr-code">
                <img
                  src={`https://api.qrserver.com/v1/create-qr-code/?size=80x80&data=${encodeURIComponent(invoice.qr_url)}`}
                  alt="QR Code"
                  width="80"
                  height="80"
                />
              </div>
            )}
          </div>
          <div className="footer-center">
            <div className="footer-text">
              Η ευθύνη για το περιεχόμενο του παραστατικού ανήκει αποκλειστικά στον εκδότη αυτού
            </div>
            <div className="footer-app">Εκδόθηκε από την εφαρμογή:</div>
          </div>
          <div className="footer-right">
            <div className="page-number">Σελίδα 1 από 1</div>
          </div>
        </div>
      </div>

      <style jsx global>{`
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        html, body {
          height: 100vh;
          font-family: 'Arial', sans-serif;
        }

        .no-print {
          display: block;
        }

        @media print {
          .no-print {
            display: none !important;
          }

          .invoice-page {
            width: 210mm;
            height: 297mm;
            margin: 0;
            padding: 15mm;
            background: white;
          }

          body {
            margin: 0;
            padding: 0;
            background: white;
          }
        }

        /* Floating Action Buttons */
        .floating-actions {
          position: fixed;
          top: 20px;
          right: 20px;
          display: flex;
          flex-direction: column;
          gap: 8px;
          z-index: 1000;
        }

        .action-btn {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          backdrop-filter: blur(10px);
          background: rgba(255, 255, 255, 0.9);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .action-btn:hover {
          background: rgba(255, 255, 255, 1);
          transform: scale(1.05);
        }

        /* Invoice Page */
        .invoice-page {
          width: 210mm;
          min-height: 297mm;
          margin: 20mm auto;
          background: white;
          font-family: 'Arial', sans-serif;
          font-size: 9px;
          line-height: 1.2;
          color: #000;
          padding: 15mm;
          box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
          position: relative;
        }

        /* Company Header */
        .company-header {
          margin-bottom: 12px;
        }

        .info-row {
          display: flex;
          border: 1px solid #000;
          border-bottom: none;
        }

        .info-row:last-child {
          border-bottom: 1px solid #000;
        }

        .info-label {
          width: 70px;
          padding: 3px 5px;
          border-right: 1px solid #000;
          background-color: #f8f9fa;
          font-weight: bold;
          font-size: 8px;
        }

        .info-value {
          flex: 1;
          padding: 3px 5px;
          font-size: 8px;
        }

        /* Invoice Type Header */
        .invoice-type-header {
          background-color: #4a90e2;
          color: white;
          text-align: center;
          padding: 6px;
          font-size: 10px;
          font-weight: bold;
          margin-bottom: 8px;
        }

        /* Invoice Details */
        .invoice-details {
          display: flex;
          gap: 8px;
          margin-bottom: 8px;
          flex-wrap: wrap;
        }

        .detail-group {
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .detail-label {
          font-size: 8px;
          font-weight: bold;
        }

        .detail-value {
          border: 1px solid #000;
          padding: 2px 4px;
          min-width: 60px;
          text-align: center;
          font-size: 8px;
        }

        /* Client Section */
        .client-section {
          margin-bottom: 12px;
        }

        .client-header {
          font-size: 9px;
          font-weight: bold;
          margin-bottom: 4px;
        }

        .client-row {
          display: flex;
          margin-bottom: 1px;
        }

        .client-label {
          width: 70px;
          font-size: 8px;
          padding: 2px 4px;
        }

        .client-value {
          flex: 1;
          background-color: #e3f2fd;
          padding: 2px 4px;
          font-size: 8px;
        }

        /* Invoice Table */
        .invoice-table {
          margin-bottom: 8px;
        }

        .table-header {
          display: grid;
          grid-template-columns: 5% 28% 9% 6% 12% 10% 12% 8% 10% 10%;
          background-color: #4a90e2;
          color: white;
          font-weight: bold;
          font-size: 8px;
        }

        .table-row {
          display: grid;
          grid-template-columns: 5% 28% 9% 6% 12% 10% 12% 8% 10% 10%;
          border-bottom: 1px solid #ddd;
          font-size: 8px;
        }

        .totals-row {
          background-color: #f8f9fa;
          font-weight: bold;
        }

        .table-header > div,
        .table-row > div {
          padding: 3px 2px;
          border-right: 1px solid #ddd;
          display: flex;
          align-items: center;
        }

        .col-code,
        .col-quantity,
        .col-unit,
        .col-vat-percent {
          justify-content: center;
        }

        .col-price,
        .col-discount,
        .col-value,
        .col-vat-amount,
        .col-total {
          justify-content: flex-end;
        }

        /* Summary Section */
        .summary-section {
          margin-bottom: 10px;
        }

        .summary-header {
          display: grid;
          grid-template-columns: repeat(8, 1fr);
          background-color: #4a90e2;
          color: white;
          font-weight: bold;
          font-size: 8px;
        }

        .summary-row {
          display: grid;
          grid-template-columns: repeat(8, 1fr);
          background-color: #f8f9fa;
          font-size: 8px;
        }

        .summary-header > div,
        .summary-row > div {
          padding: 3px 2px;
          text-align: center;
          border-right: 1px solid #ddd;
        }

        /* Total Payable */
        .total-payable {
          background-color: #e3f2fd;
          padding: 6px;
          text-align: center;
          font-size: 11px;
          font-weight: bold;
          margin-bottom: 10px;
        }

        /* Notes Section */
        .notes-section {
          border: 1px solid #000;
          min-height: 30px;
          margin-bottom: 15px;
          position: relative;
        }

        .notes-label {
          font-size: 8px;
          font-weight: bold;
          padding: 2px 4px;
        }

        /* Footer */
        .footer {
          position: absolute;
          bottom: 15mm;
          left: 15mm;
          right: 15mm;
          display: flex;
          justify-content: space-between;
          align-items: flex-end;
          font-size: 7px;
          color: #666;
        }

        .footer-left {
          width: 80px;
        }

        .footer-center {
          flex: 1;
          text-align: center;
        }

        .footer-right {
          width: 80px;
          text-align: right;
        }

        .footer-text {
          margin-bottom: 2px;
        }

        .footer-app {
          font-size: 8px;
        }

        .page-number {
          font-size: 8px;
        }
      `}</style>
    </div>
  );
}