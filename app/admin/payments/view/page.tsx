/**
 * @file app/admin/view/page.tsx
 * @description Comprehensive payments management component
 *
 * @overview
 * Full-featured payment records management system with:
 * - Advanced filtering
 * - Sorting capabilities
 * - CRUD operations
 * - Receipt upload functionality
 *
 * @features
 * - Dynamic table rendering
 * - Flexible search and filter mechanisms
 * - Real-time data synchronization
 * - File upload with validation
 *
 * @dependencies
 * - Supabase
 * - Shadcn UI Components
 * - Date manipulation libraries
 *
 * @performanceConsiderations
 * - Efficient state management
 * - Memoized callback functions
 * - Minimal re-renders
 *
 * @securityConsiderations
 * - Client-side input validation
 * - File type and size restrictions
 * - Secure file upload mechanism
 *
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @lastUpdated 2024-01-15
 */
'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Dialog, Dialog<PERSON>ontent, DialogHeader, Dialog<PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert"
import { AlertCircle as AlertCircleIcon, Upload, FileText, ArrowUpDown, Pencil, Trash2, Receipt } from 'lucide-react'
import { format, parseISO, isWithinInterval } from 'date-fns'
import { useRouter } from 'next/navigation'
import { toast } from 'sonner'
import PaymentEditDialog from '@/components/PaymentEditDialog';
import PaymentHistory from '@/components/PaymentHistory';
import { History } from 'lucide-react';

import type { Database } from '@/types/supabase'




type Program = Database['public']['Tables']['programs']['Row'];



type DbPayment = Database['public']['Tables']['pliromes']['Row'] & {
  pelates?: {
    client_name: string | null;
  } | null;
  programs?: {
    name: string | null;
  } | null;
  course_durations?: {
    duration_name: string | null;
  } | null;
}

interface PaymentProgram {
  course: string | null;
  course_duration_id: number | null;
  checkins_allowed: '8' | '12' | 'unlimited';
  price_program: number | null;
  start_program: string | null;
  money_gave: number | null;
  date_money_gave: string | null;
  way_of_payment: string | null;
  nodebt: boolean | null;
  debt: number | null;
  comments: string | null;
  end_date: string | null;
  grace_period: number | null;
}

interface Payment extends DbPayment {
  id: string;
  pelates_id: string;
  course: string;
  course_duration_id: number;
  start_program: string;
  end_date: string;
  price_program: number;
  money_gave: number;
  date_money_gave: string;
  way_of_payment: string;
  nodebt: boolean;
  debt: number;
  comments: string;
  receipt: string | null;
  grace_period: number | null;
  checkins_allowed: '8' | '12' | 'unlimited';
}

interface FilterState {
  search: string;
  startDate: string;
  endDate: string;
  paymentStartDate: string;
  paymentEndDate: string;
  status: 'all' | 'CASH' | 'VIVA' | 'WINBANK';
  program: string;
  hasReceipt: 'all' | 'yes' | 'no';
  sortField: string;
  sortDirection: 'asc' | 'desc';
}

interface PaymentTotals {
  totalPaid: number;
  totalDebt: number;
  totalPrice: number;
  count: number;
}

interface UploadState {
  status: 'idle' | 'uploading' | 'success' | 'error';
  progress: number;
  error?: string;
}



const MAX_FILE_SIZE = 5 * 1024 * 1024;
const ALLOWED_FILE_TYPES = ['application/pdf'];

export default function ViewPayments(): JSX.Element {
  // Use proper typing for all state variables
  const [payments, setPayments] = useState<Payment[]>([]);
  const [courseDurations, setCourseDurations] = useState<Database['public']['Tables']['course_durations']['Row'][]>([]);
  const [filteredPayments, setFilteredPayments] = useState<Payment[]>([]);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [paymentToDelete, setPaymentToDelete] = useState<string | null>(null);
  const [editingPayment, setEditingPayment] = useState<Payment | null>(null);
  const [programs, setPrograms] = useState<Program[]>([]);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [selectedFileName, setSelectedFileName] = useState('');
  const [uploadState, setUploadState] = useState<UploadState>({
    status: 'idle',
    progress: 0
  });
  const [paymentToUpload, setPaymentToUpload] = useState<Payment | null>(null);
  // Add these with your other state declarations
const [isHistoryOpen, setIsHistoryOpen] = useState(false);
const [selectedClientPayments, setSelectedClientPayments] = useState<Payment[]>([]);
const [selectedClientName, setSelectedClientName] = useState('');


  const defaultFilters: FilterState = {
    search: '',
    startDate: '',
    endDate: '',
    paymentStartDate: '',
    paymentEndDate: '',
    status: 'all',
    program: 'all',
    hasReceipt: 'all',
    sortField: '',
    sortDirection: 'asc'
  };
  const [filters, setFilters] = useState<FilterState>(defaultFilters);


  const [totals, setTotals] = useState<PaymentTotals>({
    totalPaid: 0,
    totalDebt: 0,
    totalPrice: 0,
    count: 0
  });

  const supabase = createClientComponentClient<Database>();
  const router = useRouter();

  // State for invoice creation
  const [creatingInvoiceForPayment, setCreatingInvoiceForPayment] = useState<string | null>(null);


  const fetchCourseDurations = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('course_durations')
        .select('id, duration_name, days')  // Include the days field
        .order('duration_name', { ascending: true });

      if (error) throw error;

      // Transform the data ensuring all fields are present
      const typedData = (data || []).map(duration => ({
        id: duration.id,
        duration_name: duration.duration_name,
        days: duration.days
      }));

      setCourseDurations(typedData);
    } catch (error) {
      console.error('Error fetching course durations:', error);
    }
  }, [supabase]);

 // Update fetchPayments to include course_durations
 const fetchPayments = useCallback(async () => {
  try {
    const { data: paymentsData, error: paymentsError } = await supabase
      .from('pliromes')
      .select(`
        *,
        pelates (client_name),
        programs (name),
        course_durations (duration_name)
      `)
      .order('date_money_gave', { ascending: false })

    if (paymentsError) {
      console.error('Error fetching payments:', paymentsError)
      return
    }

    if (!paymentsData) {
      console.log('No payments data received')
      return
    }

    // Transform and validate the data
    const validPayments = paymentsData.map(payment => ({
      ...payment,
      price_program: payment.price_program ?? 0,
      money_gave: payment.money_gave ?? 0,
      debt: payment.debt ?? 0,
      nodebt: payment.nodebt ?? false,
      comments: payment.comments ?? '',
      way_of_payment: payment.way_of_payment ?? '',
    })) as Payment[]

    console.log('Fetched payments:', validPayments)
    setPayments(validPayments)
    setFilteredPayments(validPayments)

  } catch (error) {
    console.error('Error in fetchPayments:', error)
  }
}, [supabase])

  // Update fetchPayments to include course_durations
  const fetchPrograms = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('programs')
        .select('id, name, created_at, description')  // Select all required fields
        .order('name', { ascending: true });

      if (error) throw error;

      // Transform the data to ensure all fields are properly typed
      const programsWithDefaults = (data || []).map(program => ({
        id: program.id,
        name: program.name,
        created_at: program.created_at || null,
        description: program.description || null
      }));

      setPrograms(programsWithDefaults);
    } catch (error) {
      console.error('Error fetching programs:', error);
    }
  }, [supabase]);

const handleViewHistory = useCallback((pelatesId: string, clientName: string) => {
  const clientPayments = payments.filter(p => p.pelates_id === pelatesId);
  setSelectedClientPayments(clientPayments);
  setSelectedClientName(clientName);
  setIsHistoryOpen(true);
}, [payments]);


  const [updateAlert, setUpdateAlert] = useState<{
    show: boolean;
    message: string;
    type: 'success' | 'error';
  }>({
    show: false,
    message: '',
    type: 'success'
  });

// Update useEffect to include programs fetch
useEffect(() => {
  void fetchPayments()
  void fetchCourseDurations()
  void fetchPrograms()
}, [fetchPayments, fetchCourseDurations, fetchPrograms])

  // Filter payments based on search, date range, and status
  useEffect(() => {
    let filtered = [...payments];

    // Search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(payment =>
        payment.pelates?.client_name?.toLowerCase().includes(searchLower) ||
        payment.programs?.name?.toLowerCase().includes(searchLower)
      );
    }

    // Receipt filter
    if (filters.hasReceipt !== 'all') {
      filtered = filtered.filter(payment =>
        filters.hasReceipt === 'yes' ? payment.receipt : !payment.receipt
      );
    }

    // Program filter
    if (filters.program !== 'all') {
      filtered = filtered.filter(payment => payment.course === filters.program);
    }

    // Program date filter
    if (filters.startDate && filters.endDate) {
      filtered = filtered.filter(payment => {
        try {
          const paymentDate = parseISO(payment.start_program);
          return isWithinInterval(paymentDate, {
            start: parseISO(filters.startDate),
            end: parseISO(filters.endDate)
          });
        } catch {
          return false;
        }
      });
    }

    // Payment date filter
    if (filters.paymentStartDate && filters.paymentEndDate) {
      filtered = filtered.filter(payment => {
        try {
          const paymentDate = parseISO(payment.date_money_gave);
          return isWithinInterval(paymentDate, {
            start: parseISO(filters.paymentStartDate),
            end: parseISO(filters.paymentEndDate)
          });
        } catch {
          return false;
        }
      });
    }

    // Payment method filter - Fix case sensitivity issue
    if (filters.status !== 'all') {
      filtered = filtered.filter(payment =>
        payment.way_of_payment === filters.status  // Remove toLowerCase() since we know exact values
      );
    }

// Sorting
if (filters.sortField) {
  filtered.sort((a, b) => {
    const aValue = a[filters.sortField as keyof Payment];
    const bValue = b[filters.sortField as keyof Payment];
    const direction = filters.sortDirection === 'asc' ? 1 : -1;

    // Handle null/undefined values
    if (aValue === null || aValue === undefined) return 1 * direction;
    if (bValue === null || bValue === undefined) return -1 * direction;

    // Handle string comparison
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return direction * aValue.localeCompare(bValue);
    }

    // Handle number comparison
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return direction * (aValue - bValue);
    }

    // Handle date comparison
    if (aValue instanceof Date && bValue instanceof Date) {
      return direction * (aValue.getTime() - bValue.getTime());
    }

    // Handle date strings
    if (typeof aValue === 'string' && typeof bValue === 'string' &&
        !isNaN(Date.parse(aValue)) && !isNaN(Date.parse(bValue))) {
      return direction * (Date.parse(aValue) - Date.parse(bValue));
    }

    // Default case
    return 0;
  });
}

    // Update totals and filtered payments
    const newTotals = filtered.reduce(
      (acc, payment) => ({
        totalPaid: acc.totalPaid + payment.money_gave,
        totalDebt: acc.totalDebt + payment.debt,
        totalPrice: acc.totalPrice + payment.price_program,
        count: acc.count + 1
      }),
      { totalPaid: 0, totalDebt: 0, totalPrice: 0, count: 0 }
    );

    setTotals(newTotals);
    setFilteredPayments(filtered);
  }, [filters, payments]);


  const confirmDelete = useCallback((id: string) => {
    setPaymentToDelete(id)
    setIsDeleteDialogOpen(true)
  }, [])

  const deletePayment = useCallback(async () => {
    if (!paymentToDelete) return

    try {
      const { error } = await supabase
        .from('pliromes')
        .delete()
        .eq('id', paymentToDelete)

      if (error) throw error

      setIsDeleteDialogOpen(false)
      void fetchPayments()
    } catch (error) {
      console.error('Error deleting payment:', error)
    }
  }, [paymentToDelete, supabase, fetchPayments])

  const handleEdit = useCallback((payment: Payment) => {
    setEditingPayment(payment)
    setIsEditDialogOpen(true)
  }, [])

const handleUpdatePayment = async (): Promise<void> => {
  if (!editingPayment) return;

  try {
    const updateFields: PaymentProgram = {
      course: editingPayment.course,
      course_duration_id: editingPayment.course_duration_id,
      checkins_allowed: editingPayment.checkins_allowed,
      price_program: editingPayment.price_program,
      start_program: editingPayment.start_program,
      money_gave: editingPayment.money_gave,
      date_money_gave: editingPayment.date_money_gave,
      way_of_payment: editingPayment.way_of_payment,
      nodebt: editingPayment.nodebt,
      debt: editingPayment.debt,
      comments: editingPayment.comments,
      end_date: editingPayment.end_date,
      grace_period: editingPayment.grace_period
    };

    const { error } = await supabase
      .from('pliromes')
      .update(updateFields)
      .eq('id', editingPayment.id);

    if (error) throw error;

    setIsEditDialogOpen(false);
    void fetchPayments();

    // Show success alert
    setUpdateAlert({
      show: true,
      message: 'Payment updated successfully',
      type: 'success'
    });

    // Hide alert after 3 seconds
    setTimeout(() => {
      setUpdateAlert(prev => ({ ...prev, show: false }));
    }, 3000);

  } catch (error) {
    console.error('Error updating payment:', error);
    // Show error alert
    setUpdateAlert({
      show: true,
      message: 'Error updating payment',
      type: 'error'
    });
  }
};

  const handleFilterChange = useCallback((key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }, [])

// Update handlers
const handleEditingPaymentChange = useCallback(<K extends keyof Payment>(
  field: K,
  value: Payment[K]
) => {
  setEditingPayment((prev): Payment | null =>
    prev ? { ...prev, [field]: value } : null
  );
}, []);


  // Add the file upload handlers
  const handleFileChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) {
      setSelectedFileName('');
      return;
    }

    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      alert('Please upload a PDF file only');
      e.target.value = '';
      setSelectedFileName('');
      return;
    }

    if (file.size > MAX_FILE_SIZE) {
      alert(`File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
      e.target.value = '';
      setSelectedFileName('');
      return;
    }

    setSelectedFileName(file.name);
    if (paymentToUpload) {
      void handleFileUpload(file, paymentToUpload.id);
    }
  }, [paymentToUpload]);

  const handleFileUpload = async (file: File, paymentId: string): Promise<void> => {
    try {
      setUploadState({ status: 'uploading', progress: 0 });

      const fileExt = file.name.split('.').pop();
      const fileName = `${paymentId}-${Date.now()}.${fileExt}`;

      // First check if file exists
      const { data: existingFile } = await supabase
        .storage
        .from('receipts')
        .list(`${paymentId}`);

      // If file exists, remove it first
      if (existingFile && existingFile.length > 0) {
        const { error: deleteError } = await supabase
          .storage
          .from('receipts')
          .remove([`${paymentId}/${existingFile[0].name}`]);

        if (deleteError) throw deleteError;
      }

      // Upload new file
      const { error: uploadError } = await supabase
        .storage
        .from('receipts')
        .upload(`${paymentId}/${fileName}`, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) throw uploadError;

      // Update payment record with new receipt path
      const { error: updateError } = await supabase
        .from('pliromes')
        .update({ receipt: `${paymentId}/${fileName}` })
        .eq('id', paymentId);

      if (updateError) throw updateError;

      setUploadState({ status: 'success', progress: 100 });
      void fetchPayments();

      setTimeout(() => {
        setIsUploadDialogOpen(false);
        setPaymentToUpload(null);
        setSelectedFileName('');
        setUploadState({ status: 'idle', progress: 0 });
      }, 1000);

    } catch (error) {
      console.error('Upload error:', error);
      setUploadState({
        status: 'error',
        progress: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  };


  const viewReceipt = async (receiptPath: string) => {
    try {
      const { data, error } = await supabase
        .storage
        .from('receipts')
        .createSignedUrl(receiptPath, 60); // URL expires in 60 seconds

      if (error) {
        throw error;
      }

      if (data?.signedUrl) {
        window.open(data.signedUrl, '_blank');
      }
    } catch (error) {
      console.error('Error getting receipt URL:', error);
      // Show error to user
      setUpdateAlert({
        show: true,
        message: 'Error viewing receipt',
        type: 'error'
      });
    }
  };

  // Add the upload dialog trigger handler
  const handleUploadClick = (payment: Payment) => {
    setPaymentToUpload(payment);
    setIsUploadDialogOpen(true);
  };

  // Handle creating invoice from payment
  const handleCreateInvoice = useCallback(async (payment: Payment) => {
    setCreatingInvoiceForPayment(payment.id);

    try {
      const response = await fetch('/api/invoices/create-from-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentId: payment.id
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create invoice');
      }

      toast.success('Invoice created successfully');

      // Redirect to the invoice detail page
      router.push(`/admin/invoices/${data.invoice.id}`);

    } catch (error) {
      console.error('Error creating invoice:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create invoice');
    } finally {
      setCreatingInvoiceForPayment(null);
    }
  }, [router]);

  const handleSort = useCallback((field: string) => {
    setFilters(prev => ({
      ...prev,
      sortField: field,
      sortDirection: prev.sortField === field && prev.sortDirection === 'asc' ? 'desc' : 'asc'
    }));
  }, []);

  const totalStyles = {
    wrapper: "flex items-center justify-start gap-8 p-4 bg-white",
    column: "flex flex-col items-start",
    label: "text-sm text-gray-500 font-normal",
    value: "text-xl font-semibold mt-1",
    price: "text-xl font-semibold mt-1",
    debt: "text-xl font-semibold mt-1 text-red-600",
    paid: "text-xl font-semibold mt-1 text-green-600"
  };


  return (
    <div className="space-y-4">
      {updateAlert.show && (
        <Alert variant={updateAlert.type === 'success' ? 'default' : 'destructive'}>
          <AlertCircleIcon className="h-4 w-4" />
          <AlertTitle>
            {updateAlert.type === 'success' ? 'Success' : 'Error'}
          </AlertTitle>
          <AlertDescription>
            {updateAlert.message}
          </AlertDescription>
        </Alert>
      )}
      <h2 className="text-2xl font-semibold">Payment Records</h2>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {/* Search - Moved to first position */}
        <div className="md:col-span-2">
          <Label htmlFor="search">Search Client or Program</Label>
          <Input
            id="search"
            placeholder="Search..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
          />
        </div>

        {/* Receipt Filter */}
        <div>
          <Label htmlFor="hasReceipt">Receipt Status</Label>
          <Select
            value={filters.hasReceipt}
            onValueChange={(value) => handleFilterChange('hasReceipt', value)}
          >
            <SelectTrigger id="hasReceipt">
              <SelectValue placeholder="Receipt status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="yes">Has Receipt</SelectItem>
              <SelectItem value="no">No Receipt</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Program Filter */}
        <div>
          <Label htmlFor="program">Program</Label>
          <Select
            value={filters.program}
            onValueChange={(value) => handleFilterChange('program', value)}
          >
            <SelectTrigger id="program">
              <SelectValue placeholder="Select program" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Programs</SelectItem>
              {programs.map((program) => (
                <SelectItem key={program.id} value={program.id}>
                  {program.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Start Date Filter */}
        <div>
          <Label htmlFor="startDate">Program Start Date</Label>
          <Input
            id="startDate"
            type="date"
            value={filters.startDate}
            onChange={(e) => handleFilterChange('startDate', e.target.value)}
          />
        </div>

        {/* End Date Filter */}
        <div>
          <Label htmlFor="endDate">Program End Date</Label>
          <Input
            id="endDate"
            type="date"
            value={filters.endDate}
            onChange={(e) => handleFilterChange('endDate', e.target.value)}
          />
        </div>

        {/* Payment Start Date Filter */}
        <div>
          <Label htmlFor="paymentStartDate">Payment Start Date</Label>
          <Input
            id="paymentStartDate"
            type="date"
            value={filters.paymentStartDate}
            onChange={(e) => handleFilterChange('paymentStartDate', e.target.value)}
          />
        </div>

        {/* Payment End Date Filter */}
        <div>
          <Label htmlFor="paymentEndDate">Payment End Date</Label>
          <Input
            id="paymentEndDate"
            type="date"
            value={filters.paymentEndDate}
            onChange={(e) => handleFilterChange('paymentEndDate', e.target.value)}
          />
        </div>

        {/* Payment Method Filter */}
        <div>
          <Label htmlFor="status">Payment Method</Label>
          <Select
            value={filters.status}
            onValueChange={(value: FilterState['status']) => handleFilterChange('status', value)}
          >
            <SelectTrigger id="status">
              <SelectValue placeholder="Select payment method" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Methods</SelectItem>
              <SelectItem value="CASH">Cash</SelectItem>
              <SelectItem value="VIVA">Card (Viva)</SelectItem>
              <SelectItem value="WINBANK">Bank Transfer</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

{/* Totals Section */}

<div className={totalStyles.wrapper}>
  <div className={totalStyles.column}>
    <span className={totalStyles.label}>Total Payments</span>
    <span className={totalStyles.value}>{totals.count}</span>
  </div>
  <div className={totalStyles.column}>
    <span className={totalStyles.label}>Total Price</span>
    <span className={totalStyles.price}>€{totals.totalPrice.toFixed(2)}</span>
  </div>
  <div className={totalStyles.column}>
    <span className={totalStyles.label}>Total Paid</span>
    <span className={totalStyles.paid}>€{totals.totalPaid.toFixed(2)}</span>
  </div>
  <div className={totalStyles.column}>
    <span className={totalStyles.label}>Total Debt</span>
    <span className={totalStyles.debt}>€{totals.totalDebt.toFixed(2)}</span>
  </div>
</div>

      {/* Payments Table */}
      <div className="rounded-md border">
        <div className="w-full overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {[
                  { key: 'client_name', label: 'Client', width: 'min-w-[180px]' },
                  { key: 'program_name', label: 'Program', width: 'min-w-[150px]' },
                  { key: 'start_program', label: 'Start Date', width: 'min-w-[120px]' },
                  { key: 'date_money_gave', label: 'Payment Date', width: 'min-w-[120px]' },
                  { key: 'end_date', label: 'End Date', width: 'min-w-[120px]' },
                  { key: 'price_program', label: 'Price', width: 'min-w-[100px]' },
                  { key: 'money_gave', label: 'Paid', width: 'min-w-[100px]' },
                  { key: 'debt', label: 'Debt', width: 'min-w-[100px]' },
                  { key: 'duration_name', label: 'Duration', width: 'min-w-[120px]' },
                  { key: 'grace_period', label: 'Grace', width: 'min-w-[100px]' },
                  { key: 'receipt', label: 'Receipt', width: 'min-w-[100px]' },
                  { key: 'actions', label: 'Actions', width: 'w-[80px]' }
                ].map(({ key, label, width }) => (
                  <TableHead key={key} className={width}>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort(key)}
                      className={key === 'actions' || key === 'receipt' ? 'cursor-default' : ''}
                      disabled={key === 'actions' || key === 'receipt'}
                    >
                      {label}
                      {key !== 'actions' && key !== 'receipt' && (
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPayments.map(payment => (
                <TableRow key={payment.id}>
                  <TableCell>{payment.pelates?.client_name ?? 'N/A'}</TableCell>
                  <TableCell>{payment.programs?.name ?? 'N/A'}</TableCell>
                  <TableCell>{payment.start_program ? format(parseISO(payment.start_program), 'dd/MM/yyyy') : 'N/A'}</TableCell>
                  <TableCell>{payment.date_money_gave ? format(parseISO(payment.date_money_gave), 'dd/MM/yyyy') : 'N/A'}</TableCell>
                  <TableCell>{payment.end_date ? format(parseISO(payment.end_date), 'dd/MM/yyyy') : 'N/A'}</TableCell>
                  <TableCell>€{Number(payment.price_program).toFixed(2)}</TableCell>
                  <TableCell>€{Number(payment.money_gave).toFixed(2)}</TableCell>
                  <TableCell className={payment.debt > 0 ? 'text-red-600' : 'text-green-600'}>
                    €{Number(payment.debt).toFixed(2)}
                  </TableCell>
                  <TableCell>{payment.course_durations?.duration_name ?? 'N/A'}</TableCell>
                  <TableCell>{payment.grace_period ? `${payment.grace_period} days` : 'N/A'}</TableCell>
                  <TableCell>
                    {payment.receipt ? (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700"
                        onClick={() => payment.receipt && viewReceipt(payment.receipt)}
                      >
                        <FileText className="h-4 w-4" />
                      </Button>
                    ) : (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 p-0 text-gray-600 hover:text-gray-700"
                        onClick={() => handleUploadClick(payment)}
                      >
                        <Upload className="h-4 w-4" />
                      </Button>
                    )}
                  </TableCell>
                  <TableCell>
  <div className="flex space-x-2">
    <Button
      variant="ghost"
      size="icon"
      onClick={() => handleViewHistory(payment.pelates_id!, payment.pelates?.client_name || 'Unknown')}
      className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700"
      title="View Payment History"
    >
      <History className="h-4 w-4" />
    </Button>
    <Button
      variant="ghost"
      size="icon"
      onClick={() => handleCreateInvoice(payment)}
      className="h-8 w-8 p-0 text-green-600 hover:text-green-700"
      disabled={creatingInvoiceForPayment === payment.id}
      title="Create Invoice"
    >
      {creatingInvoiceForPayment === payment.id ? (
        <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
      ) : (
        <Receipt className="h-4 w-4" />
      )}
    </Button>
    <Button
      variant="ghost"
      size="icon"
      onClick={() => handleEdit(payment)}
      className="h-8 w-8 p-0 text-gray-600 hover:text-gray-700"
      title="Edit Payment"
    >
      <Pencil className="h-4 w-4" />
    </Button>
    <Button
      variant="ghost"
      size="icon"
      onClick={() => confirmDelete(payment.id)}
      className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
      title="Delete Payment"
    >
      <Trash2 className="h-4 w-4" />
    </Button>
  </div>
</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Add the Upload Receipt Dialog */}
      <Dialog open={isUploadDialogOpen} onOpenChange={setIsUploadDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Upload Receipt</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="receipt">Receipt (PDF only, max {(MAX_FILE_SIZE / (1024 * 1024))}MB)</Label>
              <div className="flex gap-2 items-center">
                <Input
                  type="file"
                  id="receipt"
                  accept="application/pdf"
                  onChange={handleFileChange}
                  className="hidden"
                />
                <Button
                  type="button"
                  onClick={() => document.getElementById('receipt')?.click()}
                  variant="outline"
                >
                  Choose PDF File
                </Button>
                {selectedFileName && (
                  <span className="text-sm text-gray-600">
                    Selected: {selectedFileName}
                  </span>
                )}
              </div>

              {uploadState.status === 'uploading' && (
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                    style={{ width: `${uploadState.progress}%` }}
                  ></div>
                  <p className="text-sm text-gray-500 mt-1">
                    Uploading... {uploadState.progress}%
                  </p>
                </div>
              )}

              {uploadState.status === 'success' && (
                <p className="text-sm text-green-600">Upload completed successfully!</p>
              )}

              {uploadState.error && (
                <p className="text-sm text-red-600">{uploadState.error}</p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsUploadDialogOpen(false);
              setPaymentToUpload(null);
              setSelectedFileName('');
              setUploadState({ status: 'idle', progress: 0 });
            }}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
          </DialogHeader>
          <p>Are you sure you want to delete this payment?</p>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={() => void deletePayment()}>
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <PaymentEditDialog
      payment={editingPayment}
      isOpen={isEditDialogOpen}
      onClose={() => setIsEditDialogOpen(false)}
      onUpdate={handleUpdatePayment}
      programs={programs}
      courseDurations={courseDurations}
      handleEditingPaymentChange={handleEditingPaymentChange}
    />

    <PaymentHistory
      isOpen={isHistoryOpen}
      onClose={() => setIsHistoryOpen(false)}
      clientPayments={selectedClientPayments}
      clientName={selectedClientName}
    />

    </div>
  );
}
