// lib/mydata/reactPdfGenerator.tsx
import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  pdf,
  Font
} from '@react-pdf/renderer';

// Simple font registration for Greek support
try {
  Font.register({
    family: 'NotoSans',
    src: 'https://fonts.gstatic.com/s/notosans/v27/o-0IIpQlx3QUlC5A4PNb4j5Ba_2c7A.woff2'
  });
} catch (error) {
  console.warn('Font registration failed, using Helvetica:', error);
}

// Define types for invoice data (same as before)
interface InvoiceData {
  id: string;
  invoice_series: string;
  invoice_number: string;
  issue_date: string;
  client_name: string;
  client_vat: string;
  client_country: string;
  invoice_type: string;
  currency: string;
  total_net: number;
  total_vat: number;
  total_gross: number;
  mark?: string | null;
  qr_url?: string | null;
  created_at: string;
  updated_at: string;
  status: string;
}

interface InvoiceLine {
  id: string;
  invoice_id: string;
  line_number: number;
  description: string;
  quantity: number;
  unit_price: number;
  net_value: number;
  vat_category: number;
  vat_amount: number;
  income_classification_type: string;
  income_classification_category: string;
}

interface PaymentMethod {
  id: string;
  invoice_id: string;
  payment_type: number;
  amount: number;
  payment_info?: string | null;
}

interface CompanySettings {
  id: string;
  companyName: string;
  vatNumber: string;
  country: string;
  branch: string;
  address?: string;
  postalCode?: string;
  city?: string;
  profession?: string; // New field
  taxOffice?: string;  // New field (Δ.Ο.Υ.)
  defaultClassificationType: string;
  defaultClassificationCategory: string;
}

// Greek styling - matching the original PDF
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 20,
    fontFamily: 'Helvetica', // Fallback to Helvetica if NotoSans fails
    fontSize: 9,
  },

  // Header company info section
  companyHeader: {
    marginBottom: 15,
  },
  companyInfoRow: {
    flexDirection: 'row',
    marginBottom: 2,
  },
  companyLabel: {
    width: 80,
    fontSize: 9,
    fontWeight: 'bold',
    paddingLeft: 5,
    paddingVertical: 2,
    borderWidth: 1,
    borderColor: '#000000',
    backgroundColor: '#F8F9FA',
  },
  companyValue: {
    flex: 1,
    fontSize: 9,
    paddingLeft: 5,
    paddingVertical: 2,
    borderWidth: 1,
    borderColor: '#000000',
    borderLeftWidth: 0,
  },

  // Invoice type header
  invoiceTypeHeader: {
    backgroundColor: '#4A90E2',
    color: '#FFFFFF',
    textAlign: 'center',
    paddingVertical: 8,
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 10,
  },

  // Invoice details section
  invoiceDetailsSection: {
    flexDirection: 'row',
    marginBottom: 10,
    gap: 10,
  },
  invoiceDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  invoiceDetailLabel: {
    fontSize: 9,
    fontWeight: 'bold',
    paddingRight: 5,
  },
  invoiceDetailValue: {
    fontSize: 9,
    paddingLeft: 5,
    paddingVertical: 2,
    borderWidth: 1,
    borderColor: '#000000',
    minWidth: 80,
    textAlign: 'center',
  },

  // Client section
  clientSection: {
    marginBottom: 15,
  },
  clientHeader: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  clientInfoRow: {
    flexDirection: 'row',
    marginBottom: 2,
  },
  clientLabel: {
    width: 80,
    fontSize: 9,
    paddingLeft: 5,
    paddingVertical: 2,
  },
  clientValue: {
    flex: 1,
    fontSize: 9,
    paddingLeft: 5,
    paddingVertical: 2,
    backgroundColor: '#E3F2FD',
  },

  // Table styles
  table: {
    marginBottom: 10,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#4A90E2',
    color: '#FFFFFF',
    paddingVertical: 5,
    fontSize: 9,
    fontWeight: 'bold',
  },
  tableRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#DDDDDD',
    paddingVertical: 3,
    fontSize: 9,
  },

  // Table columns
  col1: { width: '8%', textAlign: 'center', paddingHorizontal: 2 }, // Κωδ.
  col2: { width: '30%', paddingHorizontal: 2 }, // Περιγραφή
  col3: { width: '10%', textAlign: 'center', paddingHorizontal: 2 }, // Ποσότητα
  col4: { width: '8%', textAlign: 'center', paddingHorizontal: 2 }, // Μ.Μ.
  col5: { width: '12%', textAlign: 'right', paddingHorizontal: 2 }, // Τιμή
  col6: { width: '10%', textAlign: 'right', paddingHorizontal: 2 }, // Έκπτωση
  col7: { width: '12%', textAlign: 'right', paddingHorizontal: 2 }, // Αξία
  col8: { width: '8%', textAlign: 'center', paddingHorizontal: 2 }, // ΦΠΑ %
  col9: { width: '10%', textAlign: 'right', paddingHorizontal: 2 }, // ΦΠΑ €
  col10: { width: '12%', textAlign: 'right', paddingHorizontal: 2 }, // Τελ. Αξία

  // Summary section
  summarySection: {
    marginTop: 10,
    marginBottom: 15,
  },
  summaryHeader: {
    flexDirection: 'row',
    backgroundColor: '#4A90E2',
    color: '#FFFFFF',
    paddingVertical: 3,
    fontSize: 9,
    fontWeight: 'bold',
  },
  summaryRow: {
    flexDirection: 'row',
    paddingVertical: 3,
    fontSize: 9,
    backgroundColor: '#F8F9FA',
  },
  summaryCol1: { width: '15%', textAlign: 'center', paddingHorizontal: 2 },
  summaryCol2: { width: '15%', textAlign: 'center', paddingHorizontal: 2 },
  summaryCol3: { width: '15%', textAlign: 'center', paddingHorizontal: 2 },
  summaryCol4: { width: '15%', textAlign: 'center', paddingHorizontal: 2 },
  summaryCol5: { width: '15%', textAlign: 'center', paddingHorizontal: 2 },
  summaryCol6: { width: '15%', textAlign: 'center', paddingHorizontal: 2 },
  summaryCol7: { width: '10%', textAlign: 'center', paddingHorizontal: 2 },

  // Total payable
  totalPayable: {
    backgroundColor: '#E3F2FD',
    paddingVertical: 8,
    textAlign: 'center',
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 15,
  },

  // Footer
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 20,
    right: 20,
    fontSize: 8,
    textAlign: 'center',
    color: '#666666',
  },

  // Page number
  pageNumber: {
    position: 'absolute',
    bottom: 50,
    right: 20,
    fontSize: 9,
  },
});

// These constants are kept for future use and reference
// Greek labels and text (currently unused but kept for future localization)
// const GREEK_LABELS = { ... };
// VAT rate mapping (currently unused but kept for future features)
// const VAT_RATES: Record<number, string> = { ... };
// Payment method mapping (currently unused but kept for future features)
// const PAYMENT_METHODS: Record<number, string> = { ... };

// Format currency for Greek locale
const formatCurrency = (amount: number, showSymbol: boolean = true): string => {
  const formatted = amount.toFixed(2).replace('.', ',');
  return showSymbol ? `${formatted}` : formatted;
};

// Format date for Greek locale
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('el-GR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  } catch (error) {
    return dateString;
  }
};

// Minimal Greek Invoice Document for testing
const GreekInvoiceDocument = ({
  invoice,
  lines,
  companySettings
}: {
  invoice: InvoiceData;
  lines: InvoiceLine[];
  companySettings: CompanySettings;
}) => {

  return (
    <Document
      title={`Invoice ${invoice.invoice_series}-${invoice.invoice_number}`}
      author={companySettings.companyName}
    >
      <Page size="A4" style={styles.page}>

        {/* Simple Header */}
        <View style={{ marginBottom: 20 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10 }}>
            {companySettings.companyName}
          </Text>
          <Text style={{ fontSize: 12, marginBottom: 5 }}>
            ΑΦΜ: {companySettings.vatNumber}
          </Text>
          <Text style={{ fontSize: 12, marginBottom: 15 }}>
            ΑΠΥ (Απόδειξη Παροχής Υπηρεσιών)
          </Text>
        </View>

        {/* Invoice Details */}
        <View style={{ marginBottom: 15 }}>
          <Text style={{ fontSize: 10, marginBottom: 3 }}>
            Σειρά: {invoice.invoice_series} | ΑΑ: {invoice.invoice_number}
          </Text>
          <Text style={{ fontSize: 10, marginBottom: 3 }}>
            Ημερομηνία: {formatDate(invoice.issue_date)}
          </Text>
          {invoice.mark && (
            <Text style={{ fontSize: 10, marginBottom: 3 }}>
              ΜΑΡΚ: {invoice.mark.substring(0, 15)}...
            </Text>
          )}
        </View>

        {/* Client */}
        <View style={{ marginBottom: 15 }}>
          <Text style={{ fontSize: 11, fontWeight: 'bold', marginBottom: 5 }}>
            Στοιχεία Πελάτη
          </Text>
          <Text style={{ fontSize: 10 }}>
            Επωνυμία: {invoice.client_name}
          </Text>
          {invoice.client_vat && (
            <Text style={{ fontSize: 10 }}>
              ΑΦΜ: {invoice.client_vat}
            </Text>
          )}
        </View>

        {/* Simple Table */}
        <View style={{ marginBottom: 15 }}>
          <View style={{
            flexDirection: 'row',
            backgroundColor: '#4A90E2',
            color: '#FFFFFF',
            padding: 5,
            fontSize: 10,
            fontWeight: 'bold'
          }}>
            <Text style={{ width: '50%' }}>Περιγραφή</Text>
            <Text style={{ width: '15%', textAlign: 'center' }}>Ποσότητα</Text>
            <Text style={{ width: '15%', textAlign: 'right' }}>Τιμή (€)</Text>
            <Text style={{ width: '20%', textAlign: 'right' }}>Σύνολο (€)</Text>
          </View>

          {lines.map((line) => (
            <View key={line.id} style={{
              flexDirection: 'row',
              padding: 5,
              fontSize: 9,
              borderBottomWidth: 1,
              borderBottomColor: '#DDDDDD'
            }}>
              <Text style={{ width: '50%' }}>{line.description}</Text>
              <Text style={{ width: '15%', textAlign: 'center' }}>{line.quantity}</Text>
              <Text style={{ width: '15%', textAlign: 'right' }}>{formatCurrency(line.unit_price, false)}</Text>
              <Text style={{ width: '20%', textAlign: 'right' }}>
                {formatCurrency(line.net_value + line.vat_amount, false)}
              </Text>
            </View>
          ))}
        </View>

        {/* Totals */}
        <View style={{
          backgroundColor: '#E3F2FD',
          padding: 10,
          textAlign: 'center',
          marginBottom: 20
        }}>
          <Text style={{ fontSize: 12, fontWeight: 'bold' }}>
            Πληρωτέο (€): {formatCurrency(invoice.total_gross, false)}
          </Text>
          <Text style={{ fontSize: 9, marginTop: 3 }}>
            (Καθαρή Αξία: {formatCurrency(invoice.total_net, false)} + ΦΠΑ: {formatCurrency(invoice.total_vat, false)})
          </Text>
        </View>

        {/* Footer */}
        <Text style={{
          position: 'absolute',
          bottom: 30,
          left: 20,
          right: 20,
          fontSize: 8,
          textAlign: 'center',
          color: '#666666'
        }}>
          Σελίδα 1 από 1 | Η ευθύνη για το περιεχόμενο του παραστατικού ανήκει αποκλειστικά στον εκδότη αυτού
        </Text>
      </Page>
    </Document>
  );
};

// Export the updated generator function
export async function generateReactInvoicePdf(
  invoice: InvoiceData,
  lines: InvoiceLine[],
  _paymentMethods: PaymentMethod[], // Prefixed with underscore to indicate intentionally unused
  companySettings: CompanySettings
): Promise<Buffer> {
  try {
    console.log('Generating Greek PDF for invoice:', invoice.invoice_series, invoice.invoice_number);

    // Validate required data
    if (!invoice || !lines || lines.length === 0 || !companySettings) {
      throw new Error('Missing required data for PDF generation');
    }

    // Create the Greek PDF document
    const document = (
      <GreekInvoiceDocument
        invoice={invoice}
        lines={lines}
        companySettings={companySettings}
      />
    );

    // Generate PDF buffer with proper handling for different @react-pdf/renderer versions
    console.log('Creating PDF instance...');
    const pdfInstance = pdf(document);

    console.log('Converting to buffer...');

    let finalBuffer: Buffer;

    try {
      // Try the direct toBuffer approach first
      const result = await pdfInstance.toBuffer();
      console.log('toBuffer() result type:', typeof result);
      console.log('toBuffer() result constructor:', result?.constructor?.name);

      if (Buffer.isBuffer(result)) {
        finalBuffer = result;
        console.log('Got Buffer directly');
      } else if (result instanceof Uint8Array) {
        finalBuffer = Buffer.from(result);
        console.log('Converted Uint8Array to Buffer');
      } else {
        // For PDFDocument objects, we need to collect the stream data
        console.log('Handling PDFDocument stream...');

        finalBuffer = await new Promise<Buffer>((resolve, reject) => {
          const chunks: Uint8Array[] = [];
          const timeout = setTimeout(() => {
            reject(new Error('PDF stream timeout after 30 seconds'));
          }, 30000);

          // Handle the PDFDocument stream
          const doc = result as {
            on: (event: string, callback: (data?: unknown) => void) => void;
            end?: () => void;
          };

          doc.on('data', (chunk: unknown) => {
            if (chunk instanceof Uint8Array || Buffer.isBuffer(chunk)) {
              console.log('Received chunk of size:', chunk.length);
              chunks.push(new Uint8Array(chunk));
            }
          });

          doc.on('end', () => {
            clearTimeout(timeout);
            // Calculate total length
            const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
            // Create a single buffer
            const buffer = Buffer.allocUnsafe(totalLength);
            let offset = 0;
            for (const chunk of chunks) {
              buffer.set(chunk, offset);
              offset += chunk.length;
            }
            console.log('Stream ended, total buffer size:', buffer.length);
            resolve(buffer);
          });

          doc.on('error', (error: unknown) => {
            clearTimeout(timeout);
            console.error('PDF stream error:', error);
            reject(error instanceof Error ? error : new Error('Unknown PDF stream error'));
          });

          // Important: End the document to trigger data flow
          if (typeof doc.end === 'function') {
            console.log('Calling doc.end() to finalize PDF...');
            doc.end();
          } else {
            console.log('No end() method found on document');
          }
        });
      }
    } catch (error) {
      console.error('PDF generation error:', error);
      throw new Error(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Validate the buffer
    if (!finalBuffer || finalBuffer.length === 0) {
      throw new Error('Generated PDF buffer is empty');
    }

    // Check if it looks like a PDF (but don't fail if header is different)
    const bufferStart = finalBuffer.subarray(0, 10);
    console.log('Buffer start (hex):', bufferStart.toString('hex'));
    console.log('Buffer start (string):', bufferStart.toString());

    const pdfHeader = finalBuffer.subarray(0, 4).toString();
    if (pdfHeader.startsWith('%PDF')) {
      console.log('Valid PDF header detected');
    } else {
      console.log('Warning: Non-standard PDF header, but continuing...');
    }

    console.log('PDF buffer ready, size:', finalBuffer.length, 'bytes');
    return finalBuffer;

  } catch (error) {
    console.error('Error generating Greek PDF:', error);
    throw new Error(`PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}