// components/mydata/EnvironmentSwitcher.tsx
'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertTriangle, CheckCircle, Settings } from 'lucide-react';
import { toast } from 'sonner';

interface EnvironmentSwitcherProps {
  currentEnvironment: 'development' | 'production';
  hasDevCredentials: boolean;
  hasProdCredentials: boolean;
}

export default function EnvironmentSwitcher({
  currentEnvironment,
  hasDevCredentials,
  hasProdCredentials
}: EnvironmentSwitcherProps) {
  const [selectedEnvironment, setSelectedEnvironment] = useState<'development' | 'production'>(currentEnvironment);
  const [isApplying, setIsApplying] = useState(false);

  const handleApplyEnvironment = async () => {
    if (selectedEnvironment === currentEnvironment) {
      toast.info('Environment is already set to ' + selectedEnvironment);
      return;
    }

    setIsApplying(true);
    
    try {
      // Set the environment variable for the session
      const response = await fetch('/api/mydata/set-environment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          environment: selectedEnvironment
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to set environment');
      }

      toast.success(`Environment switched to ${selectedEnvironment}`);
      
      // Reload the page to apply changes
      window.location.reload();
      
    } catch (error) {
      console.error('Error setting environment:', error);
      toast.error('Failed to switch environment');
    } finally {
      setIsApplying(false);
    }
  };

  const getEnvironmentStatus = (env: 'development' | 'production') => {
    const hasCredentials = env === 'development' ? hasDevCredentials : hasProdCredentials;
    return {
      hasCredentials,
      isActive: env === currentEnvironment,
      canSwitch: hasCredentials
    };
  };

  const devStatus = getEnvironmentStatus('development');
  const prodStatus = getEnvironmentStatus('production');

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          myDATA Environment Control
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Development Environment */}
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">Development</h3>
              <div className="flex items-center gap-2">
                {devStatus.isActive && (
                  <Badge variant="default">Active</Badge>
                )}
                {devStatus.hasCredentials ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
              </div>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              Uses mydataapidev.aade.gr for testing
            </p>
            <p className="text-xs text-gray-500">
              Credentials: {devStatus.hasCredentials ? 'Configured' : 'Missing'}
            </p>
          </div>

          {/* Production Environment */}
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">Production</h3>
              <div className="flex items-center gap-2">
                {prodStatus.isActive && (
                  <Badge variant="destructive">Active</Badge>
                )}
                {prodStatus.hasCredentials ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
              </div>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              Uses mydatapi.aade.gr for live invoices
            </p>
            <p className="text-xs text-gray-500">
              Credentials: {prodStatus.hasCredentials ? 'Configured' : 'Missing'}
            </p>
          </div>
        </div>

        {/* Environment Switcher */}
        <div className="space-y-3">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium">Switch Environment:</label>
            <Select
              value={selectedEnvironment}
              onValueChange={(value: 'development' | 'production') => setSelectedEnvironment(value)}
            >
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="development" disabled={!hasDevCredentials}>
                  Development {!hasDevCredentials && '(No credentials)'}
                </SelectItem>
                <SelectItem value="production" disabled={!hasProdCredentials}>
                  Production {!hasProdCredentials && '(No credentials)'}
                </SelectItem>
              </SelectContent>
            </Select>
            
            <Button
              onClick={handleApplyEnvironment}
              disabled={
                isApplying || 
                selectedEnvironment === currentEnvironment ||
                !getEnvironmentStatus(selectedEnvironment).hasCredentials
              }
              variant={selectedEnvironment === 'production' ? 'destructive' : 'default'}
            >
              {isApplying ? 'Applying...' : 'Apply'}
            </Button>
          </div>

          {selectedEnvironment === 'production' && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Warning:</strong> Switching to production will send real invoices to the Greek tax authority (AADE).
                Make sure you have valid production credentials and understand the implications.
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Instructions */}
        <div className="text-sm text-gray-600 space-y-2">
          <p><strong>Alternative methods to switch environment:</strong></p>
          <ul className="list-disc list-inside space-y-1 ml-4">
            <li>Set <code>MYDATA_ENVIRONMENT=production</code> in your .env.local file</li>
            <li>Run with <code>NODE_ENV=production npm run dev</code></li>
            <li>Build and run in production mode: <code>npm run build && npm start</code></li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}
