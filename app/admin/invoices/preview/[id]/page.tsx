// app/admin/invoices/preview/[id]/page.tsx
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { notFound } from 'next/navigation';
import FullPageGreekInvoice from '@/components/invoices/GreekInvoicePreview';
import type { Database } from '@/types/supabase';

export const dynamic = 'force-dynamic';

interface PageProps {
  params: {
    id: string;
  };
}

export default async function InvoicePreviewPage({ params }: PageProps) {
  const supabase = createServerComponentClient<Database>({ cookies });

  // Ensure user is authenticated
  const { data: { session } } = await supabase.auth.getSession();
  if (!session) {
    return notFound();
  }

  // Get invoice
  const { data: invoice, error: invoiceError } = await supabase
    .from('invoices')
    .select('*')
    .eq('id', params.id)
    .single();

  if (invoiceError || !invoice) {
    return notFound();
  }

  // Get invoice lines
  const { data: lines, error: linesError } = await supabase
    .from('invoice_lines')
    .select('*')
    .eq('invoice_id', invoice.id)
    .order('line_number');

  if (linesError) {
    console.error('Error fetching invoice lines:', linesError);
  }

  // Get payment methods
  const { data: paymentMethods, error: paymentsError } = await supabase
    .from('invoice_payment_methods')
    .select('*')
    .eq('invoice_id', invoice.id);

  if (paymentsError) {
    console.error('Error fetching payment methods:', paymentsError);
  }

  // Get company settings
  const { data: companySettings, error: settingsError } = await supabase
    .from('company_settings')
    .select('*')
    .single();

  if (settingsError) {
    console.error('Error fetching company settings:', settingsError);
  }

  // Default company settings if none found
  const defaultCompanySettings = {
    id: '1',
    companyName: 'ΓΥΜΝΑΣΤΗΡΙΟ ΛΙΦΤ Ι Κ Ε',
    vatNumber: '*********',
    country: 'GR',
    branch: '0',
    address: 'ΛΕΟΦΩΡΟΣ ΘΕΣΣΑΛΟΝΙΚΗΣ ΝΗΧΑΝΙΩΝΑΣ 30',
    postalCode: '57019',
    city: 'ΠΕΡΑΙΑ',
    profession: 'Υπηρεσίες Γυμναστηρίου',
    taxOffice: 'ΚΑΛΑΜΑΡΙΑΣ',
    defaultClassificationType: 'E3_561_001',
    defaultClassificationCategory: 'category1_1'
  };

  // Adapt invoice data to match expected types
  const adaptedInvoice = {
    ...invoice,
    client_country: invoice.client_country || 'GR',
    currency: invoice.currency || 'EUR',
    created_at: invoice.created_at || new Date().toISOString(),
    updated_at: invoice.updated_at || new Date().toISOString(),
    mark: invoice.mark || undefined,
    qr_url: invoice.qr_url || undefined
  };

  // Adapt company settings to match expected types
  const adaptedCompanySettings = companySettings ? {
    ...companySettings,
    address: companySettings.address || '',
    postalCode: companySettings.postalCode || '',
    city: companySettings.city || '',
    defaultClassificationType: companySettings.defaultClassificationType || 'E3_561_001',
    defaultClassificationCategory: companySettings.defaultClassificationCategory || 'category1_1'
  } : defaultCompanySettings;

  return (
    <FullPageGreekInvoice
      invoice={adaptedInvoice}
      lines={lines || []}
      paymentMethods={paymentMethods || []}
      companySettings={adaptedCompanySettings}
    />
  );
}