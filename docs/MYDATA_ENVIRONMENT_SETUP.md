# myDATA Environment Configuration Guide

This guide explains how to switch between development and production environments for myDATA integration.

## 🔧 Environment Overview

The system supports two myDATA environments:

### Development Environment
- **URL**: `https://mydataapidev.aade.gr/*`
- **Purpose**: Testing and development
- **Safety**: Safe for testing, no real tax implications
- **Credentials**: `MYDATA_USERNAME_DEV` and `MYDATA_SUBSCRIPTION_KEY_DEV`

### Production Environment
- **URL**: `https://mydatapi.aade.gr/myDATA/*`
- **Purpose**: Live invoice submission to Greek tax authority
- **Safety**: ⚠️ **REAL TAX IMPLICATIONS** - invoices are legally binding
- **Credentials**: `MYDATA_USERNAME_PROD` and `MYDATA_SUBSCRIPTION_KEY_PROD`

## 🔑 Setting Up Credentials

### 1. Add Environment Variables

Add these to your `.env.local` file:

```env
# Development myDATA Credentials (for testing)
MYDATA_USERNAME_DEV=your_dev_username
MYDATA_SUBSCRIPTION_KEY_DEV=your_dev_subscription_key

# Production myDATA Credentials (for live invoices)
MYDATA_USERNAME_PROD=your_production_username
MYDATA_SUBSCRIPTION_KEY_PROD=your_production_subscription_key
```

### 2. Obtain Credentials from AADE

- **Development**: Register at [myDATA Developer Portal](https://mydataapidev.aade.gr)
- **Production**: Register at [myDATA Production Portal](https://mydatapi.aade.gr)

## 🔄 Methods to Switch to Production

### Method 1: Environment Variable Override (Recommended)

Add to your `.env.local`:
```env
MYDATA_ENVIRONMENT=production
```

Then restart your application:
```bash
npm run dev
```

### Method 2: NODE_ENV Production Mode

```bash
# Build the application
npm run build

# Run in production mode
NODE_ENV=production npm start
```

### Method 3: Temporary Override

```bash
# Run development server with production myDATA
NODE_ENV=production npm run dev
```

### Method 4: Runtime Environment Variable

```bash
# Set environment variable for the session
MYDATA_ENVIRONMENT=production npm run dev
```

## 🛡️ Safety Checks

### Before Switching to Production:

1. **✅ Verify Credentials**
   - Ensure production credentials are valid
   - Test connection in development first

2. **✅ Check Company Settings**
   - Verify VAT number is correct
   - Confirm company details are accurate

3. **✅ Test Invoice Creation**
   - Create test invoices in development
   - Verify all data is correct

4. **✅ Understand Legal Implications**
   - Production invoices are legally binding
   - Cannot be easily cancelled once submitted
   - Must comply with Greek tax regulations

## 🔍 Verifying Current Environment

### Check in Application:
1. Go to `/admin/mydata/settings`
2. Look for "Environment" badge
3. Check "Credentials Status"

### Check in Code:
```typescript
import { MyDataService } from '@/lib/mydata/service';

const service = new MyDataService();
console.log('Current environment:', service.getEnvironment());
```

## 🚨 Production Warnings

When using production environment:

- **Real Invoices**: All submitted invoices are real and legally binding
- **Tax Authority**: Invoices are sent directly to AADE (Greek tax authority)
- **Compliance**: Must comply with all Greek tax regulations
- **Cancellation**: Limited ability to cancel submitted invoices
- **Audit Trail**: All submissions are logged and auditable

## 🔧 Troubleshooting

### Environment Not Switching
1. Check `.env.local` file exists and has correct variables
2. Restart the application completely
3. Clear browser cache and cookies
4. Verify no typos in environment variable names

### Credentials Not Working
1. Verify credentials are for the correct environment
2. Check for extra spaces or special characters
3. Ensure subscription is active with AADE
4. Test credentials using the connection test feature

### API Errors
1. Check rate limiting (development has stricter limits)
2. Verify XML format is correct
3. Check company settings are complete
4. Review API logs for detailed error messages

## 📝 Best Practices

1. **Always Test First**: Test thoroughly in development before production
2. **Backup Strategy**: Keep backups of important invoice data
3. **Monitor Logs**: Watch API logs for errors or issues
4. **Gradual Rollout**: Start with a few test invoices in production
5. **Documentation**: Keep records of all production submissions

## 🔗 Related Files

- `lib/mydata/service.ts` - Main service class
- `components/mydata/EnvironmentSwitcher.tsx` - UI component for switching
- `app/api/mydata/set-environment/route.ts` - API endpoint for environment switching
- `app/admin/mydata/settings/page.tsx` - Settings page

## 📞 Support

If you encounter issues:
1. Check the application logs
2. Review myDATA API documentation
3. Contact AADE support for credential issues
4. Check the GitHub repository for known issues
